from typing import Dict
import sys
import random

import config
from algorithm import *
from generate_topology import *
from multiprocessing import Pool,Manager
import json
from config import *
from multi_objective_algorithms import NSGAIIServerPlacer, MOEADServerPlacer, DifferentialEvolutionServerPlacer

def main(argv):
    problems = {}
    data = DataUtils('./dataset/basestations.csv','./dataset/userdata.csv')  #导入数据集
    distance_delay = data.distances
    base_stations = data.base_stations
    with open('data/results1.txt', 'w') as file:
        # delay_matrix = generate_topology(data, 300)
        # res_init = run_problem1(MIPServerPlacer(base_stations[:300], delay_matrix), 300)
        # file7 = open('results/MIP.txt', 'a')
        # print('MIP', "平均距离(km)={0}, 负载标准差={1}, 边缘服务器个数={2}".format(res_init[0], res_init[1], len(res_init[3])),
        #       file=file7)
        # file7.close()
        # rate_num = [1.0,1.2,1.4,1.6,1.8,2.0,2.2]
        # for rate in rate_num:  # 比例图
        # for budget in range(start_num, end_num, step):  # 预算图
        # for n in range(start_num, end_num, step):  # 基站图
        for cross_proability in [0,0.2,0.4,0.6,0.8,1]:
            for mutation_proability in [0,0.05,0.1,0.15,0.2]:
                rate = 1.0
                budget = 30
                n = 450
                # mutation_proability = 0.1
                # cross_proability = 0.8
                delay_matrix = generate_topology(data, n)
                # initial_basestations = {27: 100000, 41: 100000, 88: 200000, 109: 200000, 138: 100000, 165: 200000, 182: 100000, 194: 200000, 198: 200000, 203: 200000, 231: 200000, 241: 200000, 272: 100000, 294: 100000, 295: 200000}
                # initial_basestations = {12: 200000, 28: 200000, 39: 200000, 47: 200000, 61: 200000, 77: 100000, 88: 100000, 125: 200000,
                #                         141: 200000, 168: 200000, 192: 200000, 204: 200000, 258: 200000, 289: 300000,297: 200000}
                initial_basestations = {12: 200000, 23: 100000, 28: 200000, 39: 200000, 47: 300000, 61: 200000, 88: 100000, 125: 200000,
                                        141: 200000, 168: 200000, 192: 200000, 204: 200000, 258: 200000, 289: 300000,297: 200000}
                # problems['MIP'] = MIPServerPlacer(base_stations[:n], delay_matrix)
                problems['Top-K'] = TopKServerPlacer(base_stations[:n], delay_matrix)
                problems['Random'] = RandomServerPlacer(base_stations[:n], delay_matrix)
                problems['ACO'] = ACOServerPlacer(base_stations[:n], delay_matrix)
                problems['PSO'] = PSOServerPlacer(base_stations[:n], delay_matrix)
                # problems['PSO1'] = PSO1ServerPlacer(base_stations[:n], delay_matrix)
                problems['GA-PSO'] = GAPSOServerPlacer(base_stations[:n], delay_matrix)

                # 新增多目标优化算法
                try:
                    problems['NSGA-II'] = NSGAIIServerPlacer(base_stations[:n], delay_matrix)
                    problems['MOEA-D'] = MOEADServerPlacer(base_stations[:n], delay_matrix)
                    problems['DE'] = DifferentialEvolutionServerPlacer(base_stations[:n], delay_matrix)
                    print("Multi-objective algorithms loaded successfully")
                except Exception as e:
                    print(f"Warning: Could not load multi-objective algorithms: {e}")

                # problems['GA-PSO0'] = GAPSO0ServerPlacer(base_stations[:n], delay_matrix)   # 没有父代竞争
                # problems['GA-PSO1'] = GAPSO1ServerPlacer(base_stations[:n], delay_matrix)   # 没有父代竞争和三方交叉
                # run_problem('Random',RandomServerPlacer(base_stations[:n], delay_matrix),n,1,budget,t_max,rate,initial_basestations, cross_proability, mutation_proability,seed)
                repeat_operation(problems, n,budget,t_max,rate,initial_basestations, cross_proability, mutation_proability)


def run_problem(key, problem: ServerPlacer, n, t, budget, t_max, rate,initial_basestations, cross_proability, mutation_proability,seed):
    assert hasattr(problem, "place_server")
    result = {}
    problem.place_server(n, budget,t_max,rate,initial_basestations, cross_proability, mutation_proability,seed)
    result[t] =[problem.objective_latency(),
                problem.objective_workload(),
                problem.objective_cluster(),
                problem.objective_cluster_centers(),
                problem.objective_es_maxworkload(),
                problem.objective_fitness(),
                problem.objective_cloud()]
    file1 = open('results/{0}.txt'.format(key), 'a')
    file1.write("seed={10},N={0},t_max={1},budget={2},es_num={3},rate={4},cross_proability={11},mutation_proability={12} {5}第{6}次的fitness:{7},delay:{8},workload:{9}\n".format(n, t_max, budget,len(result[t][4]),rate,key, t + 1, result[t][5],result[t][0][0], result[t][1],seed,cross_proability,mutation_proability))
    file1.flush()
    file1.close()
    file10 = open('workload/{0}_wl.txt'.format(key), 'a')
    file10.write("seed={7}N={0},t_max={1},budget={2},es_num={3} {4}第{5}次的workload:{6}\n".format(n, t_max, budget,len(result[t][4]),key, t + 1, str(result[t][4]),seed))
    file10.flush()
    file10.close()
    file11 = open('assignment/{0}_ass.txt'.format(key), 'a')
    file11.write("seed={9}N={0},t_max={1},budget={2},es_num={3} {4}第{5}次的bs_num:{6},to_cloud:{7},assignment:{8}\n".format(n, t_max, budget,len(result[t][4]),key, t + 1,result[t][0][1],result[t][6],str(result[t][2]),seed))
    file11.flush()
    file11.close()
    return problem.objective_latency(), problem.objective_workload(), problem.objective_cluster(), problem.objective_cluster_centers()


def repeat_operation(problems: Dict[str, ServerPlacer], n, budget, t_max, rate, initial_basestations, cross_proability, mutation_proability):
    pool = Pool(13)
    for key,pro in problems.items():
        for t in range(repetitions):
            seed = 100 + t
            pool.apply_async(run_problem, (key, pro, n, t, budget, t_max,rate, initial_basestations, cross_proability, mutation_proability,seed))
    pool.close()
    pool.join()



if __name__ == '__main__':
    main(sys.argv)
