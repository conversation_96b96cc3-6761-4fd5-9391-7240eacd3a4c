from matplotlib import pyplot as plt
import numpy as np
import scipy

list_random_delay = []
list_random_workload = []
list_random_fitness = []
list_topk_delay = []
list_topk_workload = []
list_topk_fitness = []
list_aco_delay = []
list_aco_workload = []
list_aco_fitness = []
list_pso_delay = []
list_pso_workload = []
list_pso_fitness = []
list_gapso_delay = []
list_gapso_workload = []
list_gapso_fitness = []
list_gapso0_delay = []
list_gapso0_workload = []
list_gapso0_fitness = []
list_gapso1_delay = []
list_gapso1_workload = []
list_gapso1_fitness = []
list_gapso2_delay = []
list_gapso2_workload = []
list_gapso2_fitness = []
list_ga_delay = []
list_ga_workload = []
list_ga_fitness = []
with open('GAPSO_xi/results/Random.txt', 'r', encoding='utf8') as f:
    lines = f.readlines()
for line in lines:
    temp = line.split('fitness:', 1)[1].strip()
    temptemp1 = temp.split(',delay', 1)[0].strip()
    temptemp2 = temp.split(',workload:', 1)[0].strip().split(',delay:', 1)[1].strip()
    temptemp3 = temp.split(',workload:', 1)[1].strip()
    list_random_delay.append(float(temptemp2))
    list_random_workload.append(float(temptemp3))
    list_random_fitness.append(float(temptemp1))
with open('GAPSO_xi/results/Top-K.txt', 'r', encoding='utf8') as f:
    lines = f.readlines()
for line in lines:
    temp = line.split('fitness:', 1)[1].strip()
    temptemp1 = temp.split(',delay', 1)[0].strip()
    temptemp2 = temp.split(',workload:', 1)[0].strip().split(',delay:', 1)[1].strip()
    temptemp3 = temp.split(',workload:', 1)[1].strip()
    list_topk_delay.append(float(temptemp2))
    list_topk_workload.append(float(temptemp3))
    list_topk_fitness.append(float(temptemp1))
with open('GAPSO_xi/results/ACO.txt', 'r', encoding='utf8') as f:
    lines = f.readlines()
for line in lines:
    temp = line.split('fitness:', 1)[1].strip()
    temptemp1 = temp.split(',delay', 1)[0].strip()
    temptemp2 = temp.split(',workload:', 1)[0].strip().split(',delay:', 1)[1].strip()
    temptemp3 = temp.split(',workload:', 1)[1].strip()
    list_aco_delay.append(float(temptemp2))
    list_aco_workload.append(float(temptemp3))
    list_aco_fitness.append(float(temptemp1))
with open('GAPSO_xi/results/PSO.txt', 'r', encoding='utf8') as f:
    lines = f.readlines()
for line in lines:
    temp = line.split('fitness:', 1)[1].strip()
    temptemp1 = temp.split(',delay', 1)[0].strip()
    temptemp2 = temp.split(',workload:', 1)[0].strip().split(',delay:', 1)[1].strip()
    temptemp3 = temp.split(',workload:', 1)[1].strip()
    list_pso_delay.append(float(temptemp2))
    list_pso_workload.append(float(temptemp3))
    list_pso_fitness.append(float(temptemp1))
with open('GAPSO_xi/results/GA-PSO.txt', 'r', encoding='utf8') as f:
    lines = f.readlines()
for line in lines:
    temp = line.split('fitness:', 1)[1].strip()
    temptemp1 = temp.split(',delay', 1)[0].strip()
    temptemp2 = temp.split(',workload:', 1)[0].strip().split(',delay:', 1)[1].strip()
    temptemp3 = temp.split(',workload:', 1)[1].strip()
    list_gapso_delay.append(float(temptemp2))
    list_gapso_workload.append(float(temptemp3))
    list_gapso_fitness.append(float(temptemp1))
with open('GAPSO_xi/results/GA-PSO1.txt', 'r', encoding='utf8') as f:
    lines = f.readlines()
for line in lines:
    temp = line.split('fitness:', 1)[1].strip()
    temptemp1 = temp.split(',delay', 1)[0].strip()
    temptemp2 = temp.split(',workload:', 1)[0].strip().split(',delay:', 1)[1].strip()
    temptemp3 = temp.split(',workload:', 1)[1].strip()
    list_gapso1_delay.append(float(temptemp2))
    list_gapso1_workload.append(float(temptemp3))
    list_gapso1_fitness.append(float(temptemp1))
###########????????############
margin_of_error_topk1 = []
margin_of_mean_topk1 = []
margin_of_error_topk2 = []
margin_of_mean_topk2 = []
margin_of_error_topk3 = []
margin_of_mean_topk3 = []
margin_of_topk1_up = []
margin_of_topk1_low = []
margin_of_topk2_up = []
margin_of_topk2_low = []
margin_of_topk3_up = []
margin_of_topk3_low = []
margin_of_error_random1 = []
margin_of_mean_random1 = []
margin_of_error_random2 = []
margin_of_mean_random2 = []
margin_of_error_random3 = []
margin_of_mean_random3 = []
margin_of_random1_up = []
margin_of_random1_low = []
margin_of_random2_up = []
margin_of_random2_low = []
margin_of_random3_up = []
margin_of_random3_low = []
margin_of_error_aco1 = []
margin_of_mean_aco1 = []
margin_of_error_aco2 = []
margin_of_mean_aco2 = []
margin_of_error_aco3 = []
margin_of_mean_aco3 = []
margin_of_aco1_up = []
margin_of_aco1_low = []
margin_of_aco2_up = []
margin_of_aco2_low = []
margin_of_aco3_up = []
margin_of_aco3_low = []
margin_of_error_pso1 = []
margin_of_mean_pso1 = []
margin_of_error_pso2 = []
margin_of_mean_pso2 = []
margin_of_error_pso3 = []
margin_of_mean_pso3 = []
margin_of_pso1_up = []
margin_of_pso1_low = []
margin_of_pso2_up = []
margin_of_pso2_low = []
margin_of_pso3_up = []
margin_of_pso3_low = []
margin_of_error_gapso_1 = []
margin_of_mean_gapso_1 = []
margin_of_error_gapso_2 = []
margin_of_mean_gapso_2 = []
margin_of_error_gapso_3 = []
margin_of_mean_gapso_3 = []
margin_of_gapso1_up = []
margin_of_gapso1_low = []
margin_of_gapso2_up = []
margin_of_gapso2_low = []
margin_of_gapso3_up = []
margin_of_gapso3_low = []
margin_of_error_gapso1_1 = []
margin_of_mean_gapso1_1 = []
margin_of_error_gapso1_2 = []
margin_of_mean_gapso1_2 = []
margin_of_error_gapso1_3 = []
margin_of_mean_gapso1_3 = []
margin_of_gapso11_up = []
margin_of_gapso11_low = []
margin_of_gapso12_up = []
margin_of_gapso12_low = []
margin_of_gapso13_up = []
margin_of_gapso13_low = []

confidence_level = 0.95
for i in range(11):
    ###########################
    mean, sigma = np.mean(list_topk_delay[i*50:(i+1)*50]), scipy.stats.sem(list_topk_delay[i*50:(i+1)*50])
    confidence_interval = scipy.stats.norm.interval(confidence_level, loc=mean, scale=sigma)
    margin_of_error_topk1.append(confidence_interval[1]-mean)
    margin_of_mean_topk1.append(mean)
    margin_of_topk1_up.append(confidence_interval[0])
    margin_of_topk1_low.append(confidence_interval[1])
    mean, sigma = np.mean(list_topk_workload[i*50:(i+1)*50]), scipy.stats.sem(list_topk_workload[i*50:(i+1)*50])
    confidence_interval = scipy.stats.norm.interval(confidence_level, loc=mean, scale=sigma)
    margin_of_error_topk2.append(confidence_interval[1]-mean)
    margin_of_mean_topk2.append(mean)
    margin_of_topk2_up.append(confidence_interval[0])
    margin_of_topk2_low.append(confidence_interval[1])
    mean, sigma = np.mean(list_topk_fitness[i*50:(i+1)*50]), scipy.stats.sem(list_topk_fitness[i*50:(i+1)*50])
    confidence_interval = scipy.stats.norm.interval(confidence_level, loc=mean, scale=sigma)
    margin_of_error_topk3.append(confidence_interval[1]-mean)
    margin_of_mean_topk3.append(mean)
    margin_of_topk3_up.append(confidence_interval[0])
    margin_of_topk3_low.append(confidence_interval[1])
    ###########################
    mean, sigma = np.mean(list_random_delay[i*50:(i+1)*50]), scipy.stats.sem(list_random_delay[i*50:(i+1)*50])
    confidence_interval = scipy.stats.norm.interval(confidence_level, loc=mean, scale=sigma)
    margin_of_error_random1.append(confidence_interval[1]-mean)
    margin_of_mean_random1.append(mean)
    margin_of_random1_up.append(confidence_interval[0])
    margin_of_random1_low.append(confidence_interval[1])
    mean, sigma = np.mean(list_random_workload[i*50:(i+1)*50]), scipy.stats.sem(list_random_workload[i*50:(i+1)*50])
    confidence_interval = scipy.stats.norm.interval(confidence_level, loc=mean, scale=sigma)
    margin_of_error_random2.append(confidence_interval[1]-mean)
    margin_of_mean_random2.append(mean)
    margin_of_random2_up.append(confidence_interval[0])
    margin_of_random2_low.append(confidence_interval[1])
    mean, sigma = np.mean(list_random_fitness[i*50:(i+1)*50]), scipy.stats.sem(list_random_fitness[i*50:(i+1)*50])
    confidence_interval = scipy.stats.norm.interval(confidence_level, loc=mean, scale=sigma)
    margin_of_error_random3.append(confidence_interval[1]-mean)
    margin_of_mean_random3.append(mean)
    margin_of_random3_up.append(confidence_interval[0])
    margin_of_random3_low.append(confidence_interval[1])
    ###########################
    mean, sigma = np.mean(list_aco_delay[i*50:(i+1)*50]), scipy.stats.sem(list_aco_delay[i*50:(i+1)*50])
    confidence_interval = scipy.stats.norm.interval(confidence_level, loc=mean, scale=sigma)
    margin_of_error_aco1.append(confidence_interval[1]-mean)
    margin_of_mean_aco1.append(mean)
    margin_of_aco1_up.append(confidence_interval[0])
    margin_of_aco1_low.append(confidence_interval[1])
    mean, sigma = np.mean(list_aco_workload[i*50:(i+1)*50]), scipy.stats.sem(list_aco_workload[i*50:(i+1)*50])
    confidence_interval = scipy.stats.norm.interval(confidence_level, loc=mean, scale=sigma)
    margin_of_error_aco2.append(confidence_interval[1]-mean)
    margin_of_mean_aco2.append(mean)
    margin_of_aco2_up.append(confidence_interval[0])
    margin_of_aco2_low.append(confidence_interval[1])
    mean, sigma = np.mean(list_aco_fitness[i*50:(i+1)*50]), scipy.stats.sem(list_aco_fitness[i*50:(i+1)*50])
    confidence_interval = scipy.stats.norm.interval(confidence_level, loc=mean, scale=sigma)
    margin_of_error_aco3.append(confidence_interval[1]-mean)
    margin_of_mean_aco3.append(mean)
    margin_of_aco3_up.append(confidence_interval[0])
    margin_of_aco3_low.append(confidence_interval[1])
    ###########################
    mean, sigma = np.mean(list_pso_delay[i*50:(i+1)*50]), scipy.stats.sem(list_pso_delay[i*50:(i+1)*50])
    confidence_interval = scipy.stats.norm.interval(confidence_level, loc=mean, scale=sigma)
    margin_of_error_pso1.append(confidence_interval[1]-mean)
    margin_of_mean_pso1.append(mean)
    margin_of_pso1_up.append(confidence_interval[0])
    margin_of_pso1_low.append(confidence_interval[1])
    mean, sigma = np.mean(list_pso_workload[i*50:(i+1)*50]), scipy.stats.sem(list_pso_workload[i*50:(i+1)*50])
    confidence_interval = scipy.stats.norm.interval(confidence_level, loc=mean, scale=sigma)
    margin_of_error_pso2.append(confidence_interval[1]-mean)
    margin_of_mean_pso2.append(mean)
    margin_of_pso2_up.append(confidence_interval[0])
    margin_of_pso2_low.append(confidence_interval[1])
    mean, sigma = np.mean(list_pso_fitness[i*50:(i+1)*50]), scipy.stats.sem(list_pso_fitness[i*50:(i+1)*50])
    confidence_interval = scipy.stats.norm.interval(confidence_level, loc=mean, scale=sigma)
    margin_of_error_pso3.append(confidence_interval[1]-mean)
    margin_of_mean_pso3.append(mean)
    margin_of_pso3_up.append(confidence_interval[0])
    margin_of_pso3_low.append(confidence_interval[1])
    ###########################
    mean, sigma = np.mean(list_gapso_delay[i*50:(i+1)*50]), scipy.stats.sem(list_gapso_delay[i*50:(i+1)*50])
    confidence_interval = scipy.stats.norm.interval(confidence_level, loc=mean, scale=sigma)
    margin_of_error_gapso_1.append(confidence_interval[1]-mean)
    margin_of_mean_gapso_1.append(mean)
    margin_of_gapso1_up.append(confidence_interval[0])
    margin_of_gapso1_low.append(confidence_interval[1])
    mean, sigma = np.mean(list_gapso_workload[i*50:(i+1)*50]), scipy.stats.sem(list_gapso_workload[i*50:(i+1)*50])
    confidence_interval = scipy.stats.norm.interval(confidence_level, loc=mean, scale=sigma)
    margin_of_error_gapso_2.append(confidence_interval[1]-mean)
    margin_of_mean_gapso_2.append(mean)
    margin_of_gapso2_up.append(confidence_interval[0])
    margin_of_gapso2_low.append(confidence_interval[1])
    mean, sigma = np.mean(list_gapso_fitness[i*50:(i+1)*50]), scipy.stats.sem(list_gapso_fitness[i*50:(i+1)*50])
    confidence_interval = scipy.stats.norm.interval(confidence_level, loc=mean, scale=sigma)
    margin_of_error_gapso_3.append(confidence_interval[1]-mean)
    margin_of_mean_gapso_3.append(mean)
    margin_of_gapso3_up.append(confidence_interval[0])
    margin_of_gapso3_low.append(confidence_interval[1])
    ###########################
    mean, sigma = np.mean(list_gapso1_delay[i*50:(i+1)*50]), scipy.stats.sem(list_gapso1_delay[i*50:(i+1)*50])
    confidence_interval = scipy.stats.norm.interval(confidence_level, loc=mean, scale=sigma)
    margin_of_error_gapso1_1.append(confidence_interval[1]-mean)
    margin_of_mean_gapso1_1.append(mean)
    margin_of_gapso11_up.append(confidence_interval[0])
    margin_of_gapso11_low.append(confidence_interval[1])
    mean, sigma = np.mean(list_gapso1_workload[i*50:(i+1)*50]), scipy.stats.sem(list_gapso1_workload[i*50:(i+1)*50])
    confidence_interval = scipy.stats.norm.interval(confidence_level, loc=mean, scale=sigma)
    margin_of_error_gapso1_2.append(confidence_interval[1]-mean)
    margin_of_mean_gapso1_2.append(mean)
    margin_of_gapso12_up.append(confidence_interval[0])
    margin_of_gapso12_low.append(confidence_interval[1])
    mean, sigma = np.mean(list_gapso1_fitness[i*50:(i+1)*50]), scipy.stats.sem(list_gapso1_fitness[i*50:(i+1)*50])
    confidence_interval = scipy.stats.norm.interval(confidence_level, loc=mean, scale=sigma)
    margin_of_error_gapso1_3.append(confidence_interval[1]-mean)
    margin_of_mean_gapso1_3.append(mean)
    margin_of_gapso13_up.append(confidence_interval[0])
    margin_of_gapso13_low.append(confidence_interval[1])
    ###########################


plt.xticks([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1])
plt.errorbar([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_topk1, yerr=margin_of_error_topk1, fmt='o', color='b',marker=' ',capsize=3)
plt.errorbar([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_random1, yerr=margin_of_error_random1, fmt='o', color='g',marker=' ', capsize=3)
plt.errorbar([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_aco1, yerr=margin_of_error_aco1, fmt='o', color='m',marker=' ',capsize=3)
plt.errorbar([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_pso1, yerr=margin_of_error_pso1, fmt='o', color='k',marker=' ',capsize=3)
plt.errorbar([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_gapso1_1, yerr=margin_of_error_gapso1_1, fmt='o',color='tan',marker=' ',capsize=3)
plt.errorbar([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_gapso_1, yerr=margin_of_error_gapso_1, fmt='o', color='r',marker=' ',capsize=3)

# plt.fill_between([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_topk1_up, margin_of_topk1_low, color='b', alpha=0.5)
# plt.fill_between([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_random1_up, margin_of_random1_low, color='g', alpha=0.5)
# plt.fill_between([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_aco1_up, margin_of_aco1_low, color='m', alpha=0.5)
# plt.fill_between([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_pso1_up, margin_of_pso1_low, color='k', alpha=0.5)
# plt.fill_between([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_gapso11_up, margin_of_gapso11_low, color='tan', alpha=0.5)
# plt.fill_between([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_gapso1_up, margin_of_gapso1_low, color='r', alpha=0.5)

plt.plot([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_topk1, color='b',linestyle='--')
plt.plot([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_random1, color='g',linestyle='--')
plt.plot([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_aco1, color='m', linestyle='--')
plt.plot([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_pso1, color='k',linestyle='--')
plt.plot([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_gapso1_1,color='tan',linestyle='--')
plt.plot([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_gapso_1,color='r',linestyle='--')

plt.xlabel('Number of Base Station')
plt.ylabel('Average Access Delay (ms)')
plt.legend(['TopFirst','Random','ACO-Taboo', 'QPSO', 'GA','GA-PSO(Ours)'],loc='upper left')
# plt.legend(['ACO-Taboo', 'QPSO', 'GA-PSO', 'GA-PSO0','GA-PSO1'],loc='upper left')
plt.savefig('GAPSO_xi/result_fig/delay_xi',)
plt.close()

plt.xticks([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1])
plt.errorbar([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_topk2, yerr=margin_of_error_topk2, fmt='o', color='b',marker=' ',capsize=3)
plt.errorbar([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_random2, yerr=margin_of_error_random2, fmt='o', color='g',marker=' ', capsize=3)
plt.errorbar([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_aco2, yerr=margin_of_error_aco2, fmt='o', color='m',marker=' ',capsize=3)
plt.errorbar([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_pso2, yerr=margin_of_error_pso2, fmt='o', color='k',marker=' ',capsize=3)
plt.errorbar([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_gapso1_2, yerr=margin_of_error_gapso1_2, fmt='o',color='tan',marker=' ',capsize=3)
plt.errorbar([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_gapso_2, yerr=margin_of_error_gapso_2, fmt='o',color='r',marker=' ',capsize=3)


# plt.fill_between([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_topk2_up, margin_of_topk2_low, color='b', alpha=0.5)
# plt.fill_between([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_random2_up, margin_of_random2_low, color='g', alpha=0.5)
# plt.fill_between([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_aco2_up, margin_of_aco2_low, color='m', alpha=0.5)
# plt.fill_between([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_pso2_up, margin_of_pso2_low, color='k', alpha=0.5)
# plt.fill_between([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_gapso12_up, margin_of_gapso12_low, color='tan', alpha=0.5)
# plt.fill_between([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_gapso2_up, margin_of_gapso2_low, color='r', alpha=0.5)


plt.plot([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_topk2, color='b',linestyle='--')
plt.plot([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_random2, color='g',linestyle='--')
plt.plot([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_aco2, color='m', linestyle='--')
plt.plot([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_pso2, color='k',linestyle='--')
plt.plot([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_gapso1_2,color='tan',linestyle='--')
plt.plot([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_gapso_2,color='r',linestyle='--')

plt.xlabel('Number of Base Station')
plt.ylabel('Workload Standard Deviation (min)')
plt.legend(['TopFirst','Random','ACO-Taboo', 'PSO', 'GA','GA-PSO(Ours)'],loc='upper left')
# plt.legend(['ACO-Taboo', 'QPSO', 'GA-PSO', 'GA-PSO0','GA-PSO1'],loc='upper left')
plt.savefig('GAPSO_xi/result_fig/workload_xi',)
plt.close()


plt.xticks([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1])
plt.errorbar([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_topk3, yerr=margin_of_error_topk3, fmt='o', color='b',marker=' ',capsize=3)
plt.errorbar([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_random3, yerr=margin_of_error_random3, fmt='o', color='g',marker=' ', capsize=3)
plt.errorbar([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_aco3, yerr=margin_of_error_aco3, fmt='o', color='m',marker=' ',capsize=3)
plt.errorbar([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_pso3, yerr=margin_of_error_pso3, fmt='o', color='k',marker=' ',capsize=3)
plt.errorbar([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_gapso1_3, yerr=margin_of_error_gapso1_3, fmt='o',color='tan',marker=' ',capsize=3)
plt.errorbar([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_gapso_3, yerr=margin_of_error_gapso_3, fmt='o',color='r',marker=' ',capsize=3)

# plt.fill_between([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_topk3_up, margin_of_topk3_low, color='b', alpha=0.5)
# plt.fill_between([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_random3_up, margin_of_random3_low, color='g', alpha=0.5)
# plt.fill_between([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_aco3_up, margin_of_aco3_low, color='m', alpha=0.5)
# plt.fill_between([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_pso3_up, margin_of_pso3_low, color='k', alpha=0.5)
# plt.fill_between([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_gapso13_up, margin_of_gapso13_low, color='tan', alpha=0.5)
# plt.fill_between([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_gapso3_up, margin_of_gapso3_low, color='r', alpha=0.5)

plt.plot([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_topk3, color='b',linestyle='--')
plt.plot([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_random3, color='g',linestyle='--')
plt.plot([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_aco3, color='m', linestyle='--')
plt.plot([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_pso3, color='k',linestyle='--')
plt.plot([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_gapso1_3,color='tan',linestyle='--')
plt.plot([0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1], margin_of_mean_gapso_3,color='r',linestyle='--')
plt.xlabel('Number of Base Station')
plt.ylabel('The Value of F')
plt.legend(['TopFirst','Random','ACO-Taboo', 'PSO', 'GA','GA-PSO(Ours)'],loc='upper left')
# plt.legend(['ACO-Taboo', 'QPSO', 'GA-PSO', 'GA-PSO0','GA-PSO1'],loc='upper left')
plt.savefig('GAPSO_xi/result_fig/fitness_xi',)
plt.close()
